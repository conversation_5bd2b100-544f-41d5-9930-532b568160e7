#ifndef ETCDAPI_H
#define ETCDAPI_H

#include <chrono>
#include <iostream>
#include <thread>
#include <string>
#include <iostream>
#include <unistd.h>
#include <cstring>
#include <fstream>
#include <sstream>
#include <vector>

#include "etcd/Client.hpp"
#include "etcd/Response.hpp"
#include "etcd/Watcher.hpp"
#include "etcd/SyncClient.hpp"
#include "etcd/KeepAlive.hpp"

//max length for conf file line
#define CONF_DATA_LEN 1024

int get_endpoint_url(const std::string cluster_name,std::string& endpoint_url,std::string& auth_stat);
void get_cert_path(const std::string cluster_name,std::string& ca_path,std::string& cert_path,std::string& key_path);
int read_conf_data(std::string  file_name,std::string& data,std::string match_str);
std::string remove_space(std::string str);
int get_db_host(char *host, size_t host_size);

std::pair<int,std::string> comm_etcd_get(const std::string cluster_name,const std::string& key,std::string& val);

extern "C"{
int get_endpoint_url_by_lib(const char* cluster_name,
                           char* endpoint_url, size_t endpoint_size,
                           char* auth_stat, size_t auth_size);

}

#endif

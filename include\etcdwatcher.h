#ifndef _ETCD_WATCHER_H_
#define _ETCD_WATCHER_H_

#include "etcdapi.h"

/**
 * @brief  褰� etcd 涓煇涓� key 鍊煎彂鐢熷彉鍖栧悗锛屽皢鍥炶皟杩欎釜鍑芥暟銆�
 * @param  key 鍙戠敓鍙樺寲鐨� key
 * @param  val key 瀵瑰簲鐨勬柊鍊�
 */
typedef void(*etcdwatcher_event_callback)(const char *key, const char *val);

/**
 * @brief  鍒濆鍖� etcd watcher锛屽垱寤� etcd::Client銆�
 *         渚濊禆 get_endpoint_url() / get_cert_path() 绛夊嚱鏁板湪 etcdapi.cpp 宸茬粡瀹炵幇銆�
 * @return 0 琛ㄧず鎴愬姛锛岄潪 0 琛ㄧず澶辫触
 */
int etcdwatcher_init(void);

/**
 * @brief  鍚姩瀵规煇涓� key 鐨� watch
 * @param  key 瑕� watch 鐨� etcd key
 * @param  cb  鍙戠敓鍙樺寲鏃惰皟鐢ㄧ殑鍥炶皟鍑芥暟
 * @return 0 琛ㄧず鎴愬姛锛岄潪 0 琛ㄧず澶辫触
 */
int etcdwatcher_start_watch(const char *key, etcdwatcher_event_callback cb);

/**
 * @brief  閿€姣� watcher锛岄噴鏀捐祫婧愩€備細鍙栨秷鎵€鏈夊凡鍚姩鐨� watch銆�
 * @return 0 琛ㄧず鎴愬姛锛岄潪 0 琛ㄧず澶辫触
 */
 
int etcdwatcher_destroy(void);

#endif // _ETCD_WATCHER_H_

/*
 * Copyright (c) 2012-2014 CEA
 * <PERSON> <<EMAIL>>
 * contributeur : <PERSON> <<EMAIL>>
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 * - Redistributions of source code must retain the above copyright notice,
 *   this list of conditions and the following disclaimer.
 * - Redistributions in binary form must reproduce the above copyright notice,
 *   this list of conditions and the following disclaimer in the documentation
 *   and/or other materials provided with the distribution.
 * - Neither the name of Sun Microsystems, Inc. nor the names of its
 *   contributors may be used to endorse or promote products derived
 *   from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef _TIRPC_RPC_RDMA_FABRIC_H
#define _TIRPC_RPC_RDMA_FABRIC_H

#define _TEST_SELF 0
#include "probe_stats.h"

#define __LOCK_CBC_MEM 1
#define __LOCK_IN_MEM 1
#define __LOCK_OUT_MEM 1

#define __LOCK_IN_MEM_COND_WAIT 0
#define __LOCK_OUT_MEM_COND_WAIT 0

/*
 * ** match RFC-5666bis as closely as possible
 * */
struct xdr_rdma_segment {
	uint32_t handle;	/* Registered memory handle */
	uint32_t length;	/* Length of the chunk in bytes */
	uint64_t offset;	/* Chunk virtual address or offset */
};

struct xdr_read_list {
	uint32_t present;	/* 1 indicates presence */
	uint32_t position;	/* Position in XDR stream */
	struct xdr_rdma_segment target;
};

struct xdr_write_chunk {
	struct xdr_rdma_segment target;
};

struct xdr_write_list {
	uint32_t present;	/* 1 indicates presence */
	uint32_t elements;	/* Number of array elements */
	struct xdr_write_chunk entry[0];
};


struct rpc_rdma_header {
	uint32_t rdma_reads;
	uint32_t rdma_writes;
	uint32_t rdma_reply;
	/* rpc body follows */
};

struct rpc_rdma_header_nomsg {
	uint32_t rdma_reads;
	uint32_t rdma_writes;
	uint32_t rdma_reply;
};

struct rdma_msg {
	uint32_t rdma_xid;	/* Mirrors the RPC header xid */
	uint32_t rdma_vers;	/* Version of this protocol */
	uint32_t rdma_credit;	/* Buffers requested/granted */
	uint32_t rdma_type;	/* Type of message (enum rdma_proc) */
	union {
		struct rpc_rdma_header		rdma_msg;
		struct rpc_rdma_header_nomsg	rdma_nomsg;
	} rdma_body;
};


struct rdma_xprt_class_t {
	struct poolq_head rdma_xprt_list;
	bool g_rdma_xprt_init;
	RDMAXPRT *rdmaxprt;
	struct fabric_class *fab_clas;
};

#define DUMP_BYTES_PER_GROUP (4)
#define DUMP_GROUPS_PER_LINE (4)
#define DUMP_BYTES_PER_LINE (DUMP_BYTES_PER_GROUP * DUMP_GROUPS_PER_LINE)




#define m_(ptr) ((struct rdma_msg *)ptr)
#define xrl(ptr) ((struct xdr_read_list*)ptr)

typedef struct xdr_write_list wl_t;
#define xwl(ptr) ((struct xdr_write_list*)ptr)


#define x_xprt(xdrs) ((RDMAXPRT *)((xdrs)->x_lib[1]))


static inline void
xdr_rdma_skip_read_list(uint32_t **pptr)
{
	while (ntohl(xrl(*pptr)->present)) {
		*pptr += sizeof(struct xdr_read_list)
			 / sizeof(**pptr);
	}
	(*pptr)++;
}

static inline void
xdr_rdma_skip_write_list(uint32_t **pptr)
{
	if (ntohl(xwl(*pptr)->present)) {
		*pptr += (sizeof(struct xdr_write_list)
			  + sizeof(struct xdr_write_chunk)
			    * ntohl(xwl(*pptr)->elements))
			 / sizeof(**pptr);
	}
	(*pptr)++;
}

static inline void
xdr_rdma_skip_reply_array(uint32_t **pptr)
{
	if (ntohl(xwl(*pptr)->present)) {
		*pptr += (sizeof(struct xdr_write_list)
			  + sizeof(struct xdr_write_chunk)
			    * ntohl(xwl(*pptr)->elements))
			 / sizeof(**pptr);
	} else {
		(*pptr)++;
	}
}

static inline uint32_t *
xdr_rdma_get_read_list(void *data)
{
	return &m_(data)->rdma_body.rdma_msg.rdma_reads;
}

static inline uint64_t decode_hyper(uint64_t *iptr)
{
	return ((uint64_t) ntohl(((uint32_t*)iptr)[0]) << 32)
		| (ntohl(((uint32_t*)iptr)[1]));
}



static inline uint32_t *
xdr_rdma_get_write_array(void *data)
{
	uint32_t *ptr = xdr_rdma_get_read_list(data);

	xdr_rdma_skip_read_list(&ptr);

	return ptr;
}

static inline uint32_t *
xdr_rdma_get_reply_array(void *data)
{
	uint32_t *ptr = xdr_rdma_get_read_list(data);

	xdr_rdma_skip_read_list(&ptr);
	xdr_rdma_skip_write_list(&ptr);

	return ptr;
}

static inline uint32_t *
xdr_rdma_skip_header(struct rdma_msg *rmsg)
{
	uint32_t *ptr = &rmsg->rdma_body.rdma_msg.rdma_reads;

	xdr_rdma_skip_read_list(&ptr);
	xdr_rdma_skip_write_list(&ptr);
	xdr_rdma_skip_reply_array(&ptr);

	return ptr;
}



static inline uintptr_t
xdr_rdma_header_length(struct rdma_msg *rmsg)
{
	uint32_t *ptr = xdr_rdma_skip_header(rmsg);

	return ((uintptr_t)ptr - (uintptr_t)rmsg);
}


static  inline void
rpcrdma_dump_msg(struct xdr_ioq_uv *data, char *comment, uint32_t xid)
{
	char *buffer;
	uint8_t *datum = data->v.vio_head;
	int sized = ioquv_length(data);
	int buffered = (((sized / DUMP_BYTES_PER_LINE) + 1 /*partial line*/)
			* (12 /* heading */
			   + (((DUMP_BYTES_PER_GROUP * 2 /*%02X*/) + 1 /*' '*/)
			      * DUMP_GROUPS_PER_LINE)))
			      * 			+ 1 /*'\0'*/;
	int i = 0;
	int m = 0;

	xid = ntohl(xid);
	if (sized == 0) {
		__warnx(TIRPC_DEBUG_FLAG_XDR,
			"rpcrdma 0x%" PRIx32 "(%" PRIu32 ") %s?",
			xid, xid, comment);
		return;
	}
	buffer = (char *)mem_alloc(buffered);

	while (sized > i) {
		int j = sized - i;
		int k = j < DUMP_BYTES_PER_LINE ? j : DUMP_BYTES_PER_LINE;
		int l = 0;
		int r = sprintf(&buffer[m], "\n%10d:", i);	/* heading */

		if (r < 0)
			goto quit;
		m += r;

		for (; l < k; l++) {
			if (l % DUMP_BYTES_PER_GROUP == 0)
				buffer[m++] = ' ';

			r = sprintf(&buffer[m], "%02X", datum[i++]);
			if (r < 0)
				goto quit;
			m += r;
		}
	}
quit:
	buffer[m] = '\0';	/* in case of error */
	__warnx(TIRPC_DEBUG_FLAG_XDR,
		"NFS/FABRIC rpcrdma 0x%" PRIx32 "(%" PRIu32 ") %s:%s\n",
		xid, xid, comment, buffer);
	mem_free(buffer, buffered);
}





static inline uint64_t ntohl64(uint64_t arg64)
{
	uint64_t res64;

	uint32_t low = (uint32_t) (arg64 & 0x00000000FFFFFFFFLL);
	uint32_t high = (uint32_t) ((arg64 & 0xFFFFFFFF00000000LL) >> 32);

	low = ntohl(low);
	high = ntohl(high);

	res64 = (uint64_t) high + (((uint64_t) low) << 32);

	return res64;
}

static inline void conv_rdma_seg(struct xdr_rdma_segment* seg) {
	seg->handle = ntohl(seg->handle);
	seg->length = ntohl(seg->length);
	seg->offset = ntohl64(seg->offset);
}
static inline void
xdr_rdma_chunk_in(struct poolq_entry *have, u_int k, u_int m, u_int sized)
{
	/* final buffer limited to truncated length */
	IOQ_(have)->v.vio_head = IOQ_(have)->v.vio_base;
	IOQ_(have)->v.vio_tail = (char *)IOQ_(have)->v.vio_base + m;
	IOQ_(have)->v.vio_wrap = (char *)IOQ_(have)->v.vio_base + sized;

	while (0 < --k && NULL != (have = TAILQ_PREV(have, poolq_head_s, q))) {
		/* restore defaults after previous usage */
		IOQ_(have)->v.vio_head = IOQ_(have)->v.vio_base;
		IOQ_(have)->v.vio_tail =
		IOQ_(have)->v.vio_wrap = (char *)IOQ_(have)->v.vio_base + sized;
	}
}
static inline void
xdr_rdma_chunk_out(struct poolq_entry *have, u_int k, u_int m, u_int sized)
{
	/* final buffer limited to truncated length */
	IOQ_(have)->v.vio_head =
	IOQ_(have)->v.vio_tail = IOQ_(have)->v.vio_base;
	IOQ_(have)->v.vio_wrap = (char *)IOQ_(have)->v.vio_base + m;
	__warnx(TIRPC_DEBUG_FLAG_XDR,
			"%s()  iov total count:%d, [0,%d]size:%d, [last]size:%d.", __func__, k, k-1, sized, m);
	while (0 < --k && NULL != (have = TAILQ_PREV(have, poolq_head_s, q))) {
		/* restore defaults after previous usage */
		IOQ_(have)->v.vio_head =
		IOQ_(have)->v.vio_tail = IOQ_(have)->v.vio_base;
		IOQ_(have)->v.vio_wrap = (char *)IOQ_(have)->v.vio_base + sized;
	}
}
#if 0
static inline void printf_finfo(struct fi_info *info, char *args)
{
	__warnx(TIRPC_DEBUG_FLAG_XDR,
	"%s() NFS/FABRIC\n %s-1 info: \ncaps:0x%x;\nmode:0x%x;", 
	__func__, 
	args,
	info->caps,
	info->mode);
	__warnx(TIRPC_DEBUG_FLAG_XDR,
	"%s() NFS/FABRIC\n %s-2 info: txattr: \ncaps:0x%x;\nmode:0x%x;\nop_flags:0x%x;\nsize:0x%x;\niov_limit:0x%x;\nrma_iov_limit:0x%x;\ntclass:0x%x", 
	__func__, 
	args,
	info->tx_attr->caps,
	info->tx_attr->mode,
	info->tx_attr->op_flags,
	info->tx_attr->size, 
	info->tx_attr->iov_limit, 
	info->tx_attr->rma_iov_limit,
	info->tx_attr->tclass);
	__warnx(TIRPC_DEBUG_FLAG_XDR,
	"%s() NFS/FABRIC\n %s-3 info: rxattr: \ncaps:0x%x;\nmode:0x%x;\nop_flags:0x%x;\nsize:0x%x;\niov_limit:0x%x;\nrma_iov_limit:NA;\ntclass:NA", 
	__func__, 
	args,
	info->rx_attr->caps,
	info->rx_attr->mode,
	info->rx_attr->op_flags,
	info->rx_attr->size, 
	info->rx_attr->iov_limit);
	return;
}
#endif
enum rdma_proc {
	RDMA_MSG = 0,	/* An RPC call or reply msg */
	RDMA_NOMSG = 1,	/* An RPC call or reply msg - separate body */
	RDMA_ERROR = 4	/* An RPC RDMA encoding error */
};
extern struct rdma_xprt_class_t rdma_xprt_class;
static inline void 
probe_time_begin(struct rpc_rdma_cbc *cbc, int op) {
	if(rdma_xprt_class.rdmaxprt != NULL)
		rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_time_begin(&(cbc->probe_time_start), op);
}

static inline void 
probe_time_end(struct rpc_rdma_cbc *cbc, int op) {
	if(rdma_xprt_class.rdmaxprt != NULL)
		rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_time_end(&(cbc->probe_time_start), op);
}

static inline void 
probe_timespec_begin(void *ts, int op) {
	if(rdma_xprt_class.rdmaxprt != NULL)
		rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_time_begin(ts, op);

}

static inline void 
probe_timespec_end(void *ts, int op) {
	if(rdma_xprt_class.rdmaxprt != NULL)
		rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_time_end(ts, op);
}

#if 0
static inline void 
probe_stat_count_inc(RDMAXPRT *xd, int op) {
	xd->sm_dr.xprt.probe_count_inc(op);
}
static inline void 
probe_stat_count_dec(RDMAXPRT *xd, int op) {
	xd->sm_dr.xprt.probe_count_dec(op);
}
#endif
void probe_count_inc(int op);
void probe_count_dec(int op);
int rpc_fabric_reply(struct rpc_rdma_cbc *cbc);
void rpc_addrlist_hton(struct rpc_rdma_cbc *cbc);
void rpc_addrlist_ntoh(struct rpc_rdma_cbc *cbc);
RDMAXPRT * svc_rdma_fabric_xprt_create(void *fe);
void svc_rdma_fabric_xprt_destory(RDMAXPRT *xd);
int rpc_rdma_fabric_process(void *rdma_xprt, void *callback_arg, struct iovec *iov, int cnt, int ret);
int rpc_worksubmit(struct rpc_rdma_cbc *cbc);

extern struct rdma_xprt_class_t rdma_xprt_class;
int rpc_rdma_fabric_recv_callback(void *ch, void *callback_arg, struct iovec *iov, int cnt, int reslut);
int rpc_rdma_fabric_read_callback(void *ch, void *callback_arg, struct iovec *iov, int cnt, int reslut);
int rpc_rdma_fabric_send_callback(void *ch, void *callback_arg, struct iovec *iov, int cnt, int reslut);
int rpc_rdma_fabric_write_callback(void *ch, void *callback_arg, struct iovec *iov, int cnt, int reslut);
int sockaddr_to_ip_port(struct sockaddr_storage *addr, char *buf, size_t buflen);
#define __warnx_rdma(flags, ...) \
	do {					   \
		if (__ntirpc_pkg_params.debug_flags & (flags)) {	\
			__ntirpc_pkg_params.warnx_(__VA_ARGS__);	\
		}							\
	} while (0)

#endif /* !_TIRPC_RPC_RDMA_H */

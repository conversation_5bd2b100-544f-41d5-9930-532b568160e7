#include "etcdapi.h"
extern "C" {
#include "log.h"
}

const static std::string name_prefix = "etcd-"; //cluster_name = name_prefix + aa_domain_name

int get_db_host(char *host, size_t host_size) {
	int try_count = 0;	

	while (true) {
		std::string host_val = "";
		std::pair<int, std::string> recmsg;
		recmsg=comm_etcd_get("database", "/database/master_ip", host_val);
		LogInfo(COMPONENT_DATABASE, "comm_etcd_get return: code:%d, message:%s, %s", recmsg.first, recmsg.second.c_str(), host_val.c_str());
		if (recmsg.first == 0 && !host_val.empty())	{
			memset(host, 0, host_size);
			strncpy(host, host_val.c_str(), host_size-1);
			return 0;
		} else {
			try_count++;
			if (try_count >= 3){
				LogCrit(COMPONENT_DATABASE, "conn`t get database master_ip, try_count = %d", try_count);
				return -1;
			}
			sleep(1);
		}
	}
	return 0;
}


std::pair<int,std::string> comm_etcd_get(const std::string cluster_name,const std::string& key,std::string& val)
{
  //std::string ret_etcd_msg="";
  //int ret_etcd_code=0;
  int ret=0;
  std::string auth_stat = "";
  std::string ca_path = "";
  std::string cert_path = "";
  std::string key_path = "";
  std::string etcd_url = "";
  etcd::Response resp;

  //ret=get_endpoint_url(cluster_name, etcd_url,auth_stat);
  
      // 为 C 接口准备缓冲区
    char etcd_url_buffer[1024] = {0};
    char auth_stat_buffer[64] = {0};

    // 调用 C 函数
    ret = get_endpoint_url_by_lib(cluster_name.c_str(), 
                                 etcd_url_buffer, sizeof(etcd_url_buffer),
                                 auth_stat_buffer, sizeof(auth_stat_buffer));

    if (ret != 0) {
        return std::make_pair(-ret, "get_endpoint_url_by_lib failed");
    }

    // 转换回 std::string
    etcd_url = etcd_url_buffer;
    auth_stat = auth_stat_buffer;
  
  if (ret!=0) {
    return std::make_pair(-ret,std::strerror(ret));
  }
  if (etcd_url.length()==0 || auth_stat.length()==0) {
    return std::make_pair(-2,"get etcd url failed,etcd url is null or auth_stat is null.");
  }
  if (auth_stat=="true") {
    get_cert_path(cluster_name,ca_path, cert_path, key_path);
    etcd::Client etcds(etcd_url,ca_path, cert_path, key_path,"etcd");
    resp = etcds.get(key).get();
  } else {
    etcd::Client etcd(etcd_url);
    resp = etcd.get(key).get();
  }
  if (resp.error_code() == 0) {
    val=resp.value().as_string();
  }

  return std::make_pair(0-resp.error_code(),resp.error_message());
}

/*func        : get endpoint url
  description : splicing url from conf file
  input       : cluster name 
  output      : endpoint_url
  return      : 
*/
int get_endpoint_url(const std::string cluster_name,std::string& endpoint_url,std::string& auth_stat)
{
  auth_stat = "false";
  endpoint_url = "";
  
  // 构造命令字符串
  std::string cmd = "idfs config-key get config-client/etcd/endpoints";

  // 使用 popen() 执行命令并读取输出
  FILE* pipe = popen(cmd.c_str(), "r");
  LogDebug(COMPONENT_DATABASE, "start etcdctl get /database/master_ip");
  if (!pipe) {
  	  LogWarn(COMPONENT_DATABASE, "failed etcdctl get /database/master_ip");
	  return -1; // 命令执行失败
  }
  LogDebug(COMPONENT_DATABASE, "end etcdctl get /database/master_ip");
  char buffer[1024];
  while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
	  endpoint_url += buffer;
  }

  // 去除末尾换行符
  if (!endpoint_url.empty() && endpoint_url.back() == '\n') {
	  endpoint_url.pop_back();
  }

  int status = pclose(pipe);
  if (status != 0 || endpoint_url.empty()) {
	  return -1; // 命令执行失败或输出为空
  }

  return 0;
}
/*
int get_endpoint_url(const std::string cluster_name,std::string& endpoint_url,std::string& auth_stat)
{
  int ret = 0;
  std::string auth_stat_line="";
  std::string end_url="";
  std::string auth_stat_match="client-cert-auth";
  std::string end_url_match="#endpoints";
  //cluster_name=name_prefix+cluster_name;
  std::string conf_file_path="/etc/etcd/"+name_prefix+cluster_name+"/"+name_prefix+cluster_name+".yml";

  //get the auth flag
  ret=read_conf_data(conf_file_path,auth_stat_line,auth_stat_match);
  if (ret!=0) {
    return ret;
  }

  //client-cert-auth: false
  if (auth_stat_line.length()==0) {
    //std::cout<<"read auth_stat_line data null:"<<auth_stat_line<<std::endl;
    return 0;
  }
  //get string false or true
  auth_stat=auth_stat_line.substr(20,auth_stat_line.length()-20); 
  auth_stat=remove_space(auth_stat);
  //std::cout<<"auth_stat:"<<auth_stat<<std::endl;

  //endpoints="**************:42379,**************:42379,**************:42379"
  ret=read_conf_data(conf_file_path,end_url,end_url_match);
  if (ret!=0) {
    return ret;
  }
  if (end_url.length()==0) {
    //std::cout<<"read end_url data null:"<<end_url<<std::endl;
    return 0;
  }
  endpoint_url=end_url.substr(11,end_url.length()-11);
  endpoint_url=remove_space(endpoint_url);
  //std::cout<<"endpoint_url:"<<endpoint_url<<std::endl;
  return 0;
}*/

/*func        : get_cert_path
  description : get cert path from conf file
  input       : cluster name 
  output      : ca_path,cert_path,key_path
  return      : 
*/
void get_cert_path(const std::string cluster_name,std::string& ca_path,std::string& cert_path,std::string& key_path)
{
  //cluster_name=name_prefix+cluster_name;
  ca_path="/etc/etcd/"+name_prefix+cluster_name+"/ssl/ca.pem";
  cert_path="/etc/etcd/"+name_prefix+cluster_name+"/ssl/client.pem";
  key_path="/etc/etcd/"+name_prefix+cluster_name+"/ssl/client-key.pem";
}

/*func        : read_conf_data
  description : parase conf file,read the specified line
  input       : file_name,line_num 
  output      : required data
  return      : 
*/
int read_conf_data(std::string  file_name,std::string& data,std::string match_str)
{
  std::ifstream in;
  std::string::size_type pos;
  in.open(file_name);
  if (in.fail()) {
  	//ret_etcd_code=errno;
  	//ret_etcd_msg=std::strerror(errno);
    //std::cout<<"error file not exist:"<<file_name<<std::endl;
    return errno;
  }

  while (getline(in,data))  {
    pos=data.find(match_str);
    if (pos!=data.npos) {
      break;
    }
  }

  in.close();
  return 0;
}

/*func        : remove_space
  description : remove the space of string
  input       : string
  output      : string of removed space
  return      : string of removed space
*/
std::string remove_space(std::string str)
{
  str.erase(remove_if(str.begin(),str.end(),::isspace),str.end());
  return str;
}

